from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from enum import Enum


class AnalysisType(str, Enum):
    """分析类型枚举"""
    FACE = "face"
    PALM = "palm"
    AUTO = "auto"


class AnalysisRequest(BaseModel):
    """分析请求模型"""
    analysis_type: AnalysisType = AnalysisType.AUTO

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AnalysisResult(BaseModel):
    """分析结果模型"""
    id: Optional[str] = None
    type: str = Field(..., description="分析类型：face或palm")
    analysis: str = Field(..., description="详细分析内容")
    features: List[str] = Field(..., description="主要特征列表")
    fortune: str = Field(..., description="运势分析")
    suggestions: List[str] = Field(..., description="建议列表")
    confidence: float = Field(..., ge=0, le=1, description="分析置信度")
    created_at: Optional[datetime] = None
    user_id: Optional[str] = None
    image_url: Optional[str] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class AnalysisHistory(BaseModel):
    """分析历史模型"""
    id: str
    user_id: str
    analysis_result: AnalysisResult
    created_at: datetime

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }