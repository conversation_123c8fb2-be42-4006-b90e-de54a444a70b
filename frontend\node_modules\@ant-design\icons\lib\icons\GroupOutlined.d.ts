import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![group](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik05MTIgODIwLjFWMjAzLjljMjgtOS45IDQ4LTM2LjYgNDgtNjcuOSAwLTM5LjgtMzIuMi03Mi03Mi03Mi0zMS4zIDAtNTggMjAtNjcuOSA0OEgyMDMuOUMxOTQgODQgMTY3LjMgNjQgMTM2IDY0Yy0zOS44IDAtNzIgMzIuMi03MiA3MiAwIDMxLjMgMjAgNTggNDggNjcuOXY2MTYuMkM4NCA4MzAgNjQgODU2LjcgNjQgODg4YzAgMzkuOCAzMi4yIDcyIDcyIDcyIDMxLjMgMCA1OC0yMCA2Ny45LTQ4aDYxNi4yYzkuOSAyOCAzNi42IDQ4IDY3LjkgNDggMzkuOCAwIDcyLTMyLjIgNzItNzIgMC0zMS4zLTIwLTU4LTQ4LTY3Ljl6TTg4OCAxMTJjMTMuMyAwIDI0IDEwLjcgMjQgMjRzLTEwLjcgMjQtMjQgMjQtMjQtMTAuNy0yNC0yNCAxMC43LTI0IDI0LTI0ek0xMzYgOTEyYy0xMy4zIDAtMjQtMTAuNy0yNC0yNHMxMC43LTI0IDI0LTI0IDI0IDEwLjcgMjQgMjQtMTAuNyAyNC0yNCAyNHptMC03NTJjLTEzLjMgMC0yNC0xMC43LTI0LTI0czEwLjctMjQgMjQtMjQgMjQgMTAuNyAyNCAyNC0xMC43IDI0LTI0IDI0em03MDQgNjgwSDE4NFYxODRoNjU2djY1NnptNDggNzJjLTEzLjMgMC0yNC0xMC43LTI0LTI0czEwLjctMjQgMjQtMjQgMjQgMTAuNyAyNCAyNC0xMC43IDI0LTI0IDI0eiIgLz48cGF0aCBkPSJNMjg4IDQ3NGg0NDhjOC44IDAgMTYtNy4yIDE2LTE2VjI4MmMwLTguOC03LjItMTYtMTYtMTZIMjg4Yy04LjggMC0xNiA3LjItMTYgMTZ2MTc2YzAgOC44IDcuMiAxNiAxNiAxNnptNTYtMTM2aDMzNnY2NEgzNDR2LTY0em0tNTYgNDIwaDQ0OGM4LjggMCAxNi03LjIgMTYtMTZWNTY2YzAtOC44LTcuMi0xNi0xNi0xNkgyODhjLTguOCAwLTE2IDcuMi0xNiAxNnYxNzZjMCA4LjggNy4yIDE2IDE2IDE2em01Ni0xMzZoMzM2djY0SDM0NHYtNjR6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
