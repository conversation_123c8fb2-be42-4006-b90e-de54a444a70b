import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![ordered-list](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyMCA3NjBIMzM2Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDU4NGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHptMC01NjhIMzM2Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDU4NGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHptMCAyODRIMzM2Yy00LjQgMC04IDMuNi04IDh2NTZjMCA0LjQgMy42IDggOCA4aDU4NGM0LjQgMCA4LTMuNiA4LTh2LTU2YzAtNC40LTMuNi04LTgtOHpNMjE2IDcxMkgxMDBjLTIuMiAwLTQgMS44LTQgNHYzNGMwIDIuMiAxLjggNCA0IDRoNzIuNHYyMC41aC0zNS43Yy0yLjIgMC00IDEuOC00IDR2MzRjMCAyLjIgMS44IDQgNCA0aDM1LjdWODM4SDEwMGMtMi4yIDAtNCAxLjgtNCA0djM0YzAgMi4yIDEuOCA0IDQgNGgxMTZjMi4yIDAgNC0xLjggNC00VjcxNmMwLTIuMi0xLjgtNC00LTR6TTEwMCAxODhoMzh2MTIwYzAgMi4yIDEuOCA0IDQgNGg0MGMyLjIgMCA0LTEuOCA0LTRWMTUyYzAtNC40LTMuNi04LTgtOGgtNzhjLTIuMiAwLTQgMS44LTQgNHYzNmMwIDIuMiAxLjggNCA0IDR6bTExNiAyNDBIMTAwYy0yLjIgMC00IDEuOC00IDR2MzZjMCAyLjIgMS44IDQgNCA0aDY4LjRsLTcwLjMgNzcuN2E4LjMgOC4zIDAgMDAtMi4xIDUuNFY1OTJjMCAyLjIgMS44IDQgNCA0aDExNmMyLjIgMCA0LTEuOCA0LTR2LTM2YzAtMi4yLTEuOC00LTQtNGgtNjguNGw3MC4zLTc3LjdhOC4zIDguMyAwIDAwMi4xLTUuNFY0MzJjMC0yLjItMS44LTQtNC00eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
