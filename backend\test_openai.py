#!/usr/bin/env python3
"""
直接测试OpenAI API连接
"""

import os
from dotenv import load_dotenv
from openai import OpenAI
import base64
from PIL import Image
from io import BytesIO

# 加载环境变量
load_dotenv()

def create_test_image():
    """创建一个测试图片"""
    img = Image.new('RGB', (300, 300), color='white')
    img_bytes = BytesIO()
    img.save(img_bytes, format='JPEG')
    return img_bytes.getvalue()

def test_openai_connection():
    """测试OpenAI连接"""
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    model = os.getenv("OPENAI_MODEL", "gpt-4-vision-preview")
    
    print(f"API Key: {api_key[:10] if api_key else 'None'}...")
    print(f"Base URL: {base_url}")
    print(f"Model: {model}")
    
    if not api_key:
        print("❌ API密钥未配置")
        return False
    
    try:
        # 创建客户端
        if base_url:
            client = OpenAI(api_key=api_key, base_url=base_url)
        else:
            client = OpenAI(api_key=api_key)
        
        print("✅ OpenAI客户端创建成功")
        
        # 创建测试图片
        image_bytes = create_test_image()
        base64_image = base64.b64encode(image_bytes).decode('utf-8')
        
        print("🖼️ 测试图片创建成功")
        
        # 测试API调用
        print("🔄 正在调用OpenAI API...")
        
        response = client.chat.completions.create(
            model=model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "请简单描述这张图片，用中文回答。"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            max_tokens=500,
            temperature=0.7
        )
        
        result = response.choices[0].message.content
        print("✅ OpenAI API调用成功!")
        print(f"响应: {result}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI API调用失败: {e}")
        return False

if __name__ == "__main__":
    print("=== OpenAI API连接测试 ===\n")
    success = test_openai_connection()
    
    if success:
        print("\n🎉 OpenAI配置正确，API工作正常!")
    else:
        print("\n💥 OpenAI配置有问题，请检查配置")
