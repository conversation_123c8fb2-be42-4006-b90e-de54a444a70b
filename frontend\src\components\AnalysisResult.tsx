import React from 'react';
import { Card, Typography, Divider, Result, Spin } from 'antd';
import { EyeOutlined, PictureOutlined } from '@ant-design/icons';
import type { AnalysisResult } from '../types';

const { Title, Paragraph } = Typography;

interface AnalysisResultProps {
  loading: boolean;
  result: AnalysisResult | null;
}

const AnalysisResultComponent: React.FC<AnalysisResultProps> = ({ loading, result }) => {
  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <p className="loading-text">AI正在分析中，请稍候...</p>
      </div>
    );
  }

  if (!result) {
    return (
      <Result
        icon={<PictureOutlined />}
        title="等待上传图片"
        subTitle="请上传面相或手相图片开始分析"
      />
    );
  }

  return (
    <div className="analysis-result">
      <Title level={4}>
        {result.type === 'face' ? '面相分析' : '手相分析'}
      </Title>
      <Paragraph>{result.analysis}</Paragraph>
      
      <Divider />
      
      <Title level={5}>主要特征：</Title>
      <ul>
        {result.features.map((feature, index) => (
          <li key={index}>{feature}</li>
        ))}
      </ul>
      
      <Divider />
      
      <Title level={5}>运势分析：</Title>
      <Paragraph>{result.fortune}</Paragraph>
      
      <Divider />
      
      <Title level={5}>建议：</Title>
      <ul>
        {result.suggestions.map((suggestion, index) => (
          <li key={index}>{suggestion}</li>
        ))}
      </ul>
    </div>
  );
};

export default AnalysisResultComponent;
