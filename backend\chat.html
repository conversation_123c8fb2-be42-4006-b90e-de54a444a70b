<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天测试 - 麻衣神相</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .status-loading { background: #ff9800; animation: pulse 1s infinite; }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.user .message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }

        .message-time {
            font-size: 12px;
            color: #999;
            margin-top: 5px;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .send-button:hover {
            background: #5a6fd8;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .info-message {
            background: #e3f2fd;
            color: #1565c0;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #1565c0;
        }

        .warning-message {
            background: #fff3e0;
            color: #ef6c00;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #ef6c00;
        }

        .loading {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #666;
            font-style: italic;
        }

        .loading-dots {
            display: inline-block;
        }

        .loading-dots::after {
            content: '';
            animation: dots 1.5s infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🔮 AI聊天测试</h1>
            <p>
                <span id="status-indicator" class="status-indicator status-offline"></span>
                <span id="status-text">检查连接中...</span>
            </p>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message ai">
                <div class="message-content">
                    <div>你好！我是麻衣神相AI助手。你可以问我任何问题来测试API连接。</div>
                    <div class="message-time" id="welcomeTime"></div>
                </div>
            </div>
            <div id="connectionInfo"></div>
        </div>
        
        <div class="chat-input">
            <div class="input-group">
                <input 
                    type="text" 
                    class="message-input" 
                    id="messageInput" 
                    placeholder="输入消息测试AI连接..."
                    disabled
                >
                <button class="send-button" id="sendButton" disabled>发送</button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');

        // 初始化时间
        document.getElementById('welcomeTime').textContent = new Date().toLocaleTimeString();

        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();

                // 显示连接信息
                showConnectionInfo(data);

                if (data.openai_available) {
                    statusIndicator.className = 'status-indicator status-online';
                    statusText.textContent = 'OpenAI API 已连接';
                } else {
                    statusIndicator.className = 'status-indicator status-offline';
                    statusText.textContent = 'OpenAI API 未连接 (使用模拟模式)';
                }

                messageInput.disabled = false;
                sendButton.disabled = false;
                messageInput.placeholder = '输入消息开始聊天...';

            } catch (error) {
                statusIndicator.className = 'status-indicator status-offline';
                statusText.textContent = '服务器连接失败';
                showError('无法连接到服务器，请确保后端服务正在运行');
            }
        }

        // 显示连接信息
        function showConnectionInfo(data) {
            const infoDiv = document.getElementById('connectionInfo');
            const apiStatus = data.openai_available ? '✅ 已连接' : '❌ 未连接';
            const dbStatus = data.supabase_available ? '✅ 已连接' : '❌ 未连接';

            infoDiv.innerHTML = `
                <div class="info-message">
                    <strong>🔧 系统状态</strong><br>
                    OpenAI API: ${apiStatus}<br>
                    数据库: ${dbStatus}<br>
                    版本: ${data.version}
                </div>
            `;

            if (!data.openai_available) {
                infoDiv.innerHTML += `
                    <div class="warning-message">
                        <strong>⚠️ 注意</strong><br>
                        OpenAI API暂时不可用，系统将使用模拟回复。<br>
                        请检查API配置或网络连接。
                    </div>
                `;
            }
        }

        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // 显示用户消息
            addMessage(message, 'user');
            messageInput.value = '';
            
            // 显示加载状态
            const loadingId = addLoadingMessage();
            
            // 禁用输入
            messageInput.disabled = true;
            sendButton.disabled = true;
            statusIndicator.className = 'status-indicator status-loading';
            statusText.textContent = 'AI思考中...';

            try {
                const response = await fetch(`${API_BASE}/api/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message })
                });

                const data = await response.json();
                
                // 移除加载消息
                removeLoadingMessage(loadingId);
                
                if (response.ok) {
                    addMessage(data.response, 'ai', data.model);
                } else {
                    showError(data.detail || '发送消息失败');
                }
                
            } catch (error) {
                removeLoadingMessage(loadingId);
                showError('网络错误，请重试');
            } finally {
                // 恢复输入
                messageInput.disabled = false;
                sendButton.disabled = false;
                messageInput.focus();
                checkServerStatus(); // 重新检查状态
            }
        }

        // 添加消息到聊天界面
        function addMessage(content, type, model = '') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            const time = new Date().toLocaleTimeString();
            const modelInfo = model && model !== 'mock' ? ` (${model})` : '';
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div>${content}</div>
                    <div class="message-time">${time}${modelInfo}</div>
                </div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 添加加载消息
        function addLoadingMessage() {
            const loadingId = 'loading-' + Date.now();
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ai';
            messageDiv.id = loadingId;
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="loading">
                        <span>AI正在思考</span>
                        <span class="loading-dots"></span>
                    </div>
                </div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            return loadingId;
        }

        // 移除加载消息
        function removeLoadingMessage(loadingId) {
            const loadingElement = document.getElementById(loadingId);
            if (loadingElement) {
                loadingElement.remove();
            }
        }

        // 显示错误消息
        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            chatMessages.appendChild(errorDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 事件监听
        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !messageInput.disabled) {
                sendMessage();
            }
        });

        // 初始化
        checkServerStatus();
        
        // 定期检查状态
        setInterval(checkServerStatus, 30000);
    </script>
</body>
</html>
