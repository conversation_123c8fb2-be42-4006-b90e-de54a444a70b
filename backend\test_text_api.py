#!/usr/bin/env python3
"""
测试文本API连接
"""

import os
from dotenv import load_dotenv
from openai import OpenAI

# 加载环境变量
load_dotenv()

def test_text_api():
    """测试文本API"""
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    
    print(f"API Key: {api_key[:10] if api_key else 'None'}...")
    print(f"Base URL: {base_url}")
    
    if not api_key:
        print("❌ API密钥未配置")
        return False
    
    try:
        # 创建客户端
        if base_url:
            client = OpenAI(api_key=api_key, base_url=base_url)
        else:
            client = OpenAI(api_key=api_key)
        
        print("✅ OpenAI客户端创建成功")
        
        # 测试简单的文本API调用
        print("🔄 正在调用文本API...")
        
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",  # 使用更常见的模型
            messages=[
                {
                    "role": "user",
                    "content": "你好，请用中文回复。"
                }
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        result = response.choices[0].message.content
        print("✅ 文本API调用成功!")
        print(f"响应: {result}")
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 文本API连接测试 ===\n")
    success = test_text_api()
    
    if success:
        print("\n🎉 API连接正常!")
    else:
        print("\n💥 API连接有问题")
