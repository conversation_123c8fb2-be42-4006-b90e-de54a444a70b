import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![translation](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0xNDAgMTg4aDU4NHYxNjRoNzZWMTQ0YzAtMTcuNy0xNC4zLTMyLTMyLTMySDk2Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNTQ0di03NkgxNDBWMTg4eiIgLz48cGF0aCBkPSJNNDE0LjMgMjU2aC02MC42Yy0zLjQgMC02LjQgMi4yLTcuNiA1LjRMMjE5IDYyOS40Yy0uMy44LS40IDEuNy0uNCAyLjYgMCA0LjQgMy42IDggOCA4aDU1LjFjMy40IDAgNi40LTIuMiA3LjYtNS40TDMyMiA1NDBoMTk2LjJMNDIyIDI2MS40YTguNDIgOC40MiAwIDAwLTcuNy01LjR6bTEyLjQgMjI4aC04NS41TDM4NCAzNjAuMiA0MjYuNyA0ODR6TTkzNiA1MjhIODAwdi05M2MwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2OTNINTkyYy0xMy4zIDAtMjQgMTAuNy0yNCAyNHYxNzZjMCAxMy4zIDEwLjcgMjQgMjQgMjRoMTM2djE1MmMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04Vjc1MmgxMzZjMTMuMyAwIDI0LTEwLjcgMjQtMjRWNTUyYzAtMTMuMy0xMC43LTI0LTI0LTI0ek03MjggNjgwaC04OHYtODBoODh2ODB6bTE2MCAwaC04OHYtODBoODh2ODB6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
