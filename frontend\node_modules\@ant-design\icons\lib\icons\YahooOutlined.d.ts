import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![yahoo](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1OS45IDY4MS40aC0xNC4xYy0yNy4xIDAtNDkuMiAyMi4yLTQ5LjIgNDkuM3YxNC4xYzAgMjcuMSAyMi4yIDQ5LjMgNDkuMiA0OS4zaDE0LjFjMjcuMSAwIDQ5LjItMjIuMiA0OS4yLTQ5LjN2LTE0LjFjMC0yNy4xLTIyLjItNDkuMy00OS4yLTQ5LjN6TTQwMi42IDIzMUMyMTYuMiAyMzEgNjUgMzU3IDY1IDUxMi41UzIxNi4yIDc5NCA0MDIuNiA3OTRzMzM3LjYtMTI2IDMzNy42LTI4MS41UzU4OS4xIDIzMSA0MDIuNiAyMzF6bTAgNTA3QzI0NS4xIDczOCAxMjEgNjM0LjYgMTIxIDUxMi41YzAtNjIuMyAzMi4zLTExOS43IDg0LjktMTYxdjQ4LjRoMzdsMTU5LjggMTU5Ljl2NjUuM2gtODQuNHY1Ni4zaDIyNS4xdi01Ni4zSDQ1OXYtNjUuM2wxMDMuNS0xMDMuNmg2NS4zdi01Ni4zSDQ1OXY2NS4zbC0yOC4xIDI4LjEtOTMuNC05My41aDM3di01Ni4zSDIxNi40YzQ5LjQtMzUgMTE0LjMtNTYuNiAxODYuMi01Ni42IDE1Ny42IDAgMjgxLjYgMTAzLjQgMjgxLjYgMjI1LjVTNTYwLjIgNzM4IDQwMi42IDczOHptNTM0LjctNTA3SDgyNC43Yy0xNS41IDAtMjcuNyAxMi42LTI3LjEgMjguMWwxMy4xIDM2Nmg4NC40bDY1LjQtMzY2LjRjMi43LTE1LjItNy44LTI3LjctMjMuMi0yNy43eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
