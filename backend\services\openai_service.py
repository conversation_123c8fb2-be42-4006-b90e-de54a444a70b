import base64
import logging
import os
import sys
from typing import Dict, Any, Optional
from openai import OpenAI
from core.config import settings

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
try:
    from utils.proxy_helper import configure_openai_proxy
except ImportError:
    def configure_openai_proxy():
        return None

logger = logging.getLogger(__name__)


class OpenAIService:
    """OpenAI服务类"""
    
    def __init__(self):
        # 自动配置代理
        proxy_config = configure_openai_proxy()
        if proxy_config:
            logger.info(f"已自动配置代理: {proxy_config}")

        # 初始化OpenAI客户端，支持第三方API
        client_kwargs = {"api_key": settings.openai_api_key}
        if settings.openai_base_url:
            client_kwargs["base_url"] = settings.openai_base_url
            logger.info(f"Using custom OpenAI base URL: {settings.openai_base_url}")

        self.client = OpenAI(**client_kwargs)
        self.model = settings.openai_model
        self.max_tokens = settings.openai_max_tokens
        self.temperature = settings.openai_temperature
    
    def encode_image(self, image_bytes: bytes) -> str:
        """将图片编码为base64"""
        return base64.b64encode(image_bytes).decode('utf-8')
    
    async def analyze_physiognomy(self, image_bytes: bytes, analysis_type: str = "auto") -> Dict[str, Any]:
        """
        分析面相或手相
        
        Args:
            image_bytes: 图片字节数据
            analysis_type: 分析类型 ("face", "palm", "auto")
        
        Returns:
            分析结果字典
        """
        try:
            # 编码图片
            base64_image = self.encode_image(image_bytes)
            
            # 构建提示词
            prompt = self._build_prompt(analysis_type)
            
            # 调用OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )
            
            # 解析响应
            analysis_text = response.choices[0].message.content
            result = self._parse_analysis(analysis_text, analysis_type)
            
            logger.info(f"Successfully analyzed {analysis_type} image")
            return result

        except Exception as e:
            logger.error(f"Error analyzing image: {e}")
            raise

    async def chat(self, message: str) -> str:
        """
        简单的聊天功能

        Args:
            message: 用户消息

        Returns:
            AI回复
        """
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": message
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error in chat: {e}")
            raise

    def _build_prompt(self, analysis_type: str) -> str:
        """构建分析提示词"""
        base_prompt = """
你是一位精通《麻衣神相》的专业相术师。请根据传统相术理论分析这张图片。

请按照以下格式返回分析结果（使用JSON格式）：
{
    "type": "face或palm",
    "analysis": "详细的相术分析，基于《麻衣神相》理论",
    "features": ["特征1", "特征2", "特征3"],
    "fortune": "运势分析和预测",
    "suggestions": ["建议1", "建议2", "建议3"],
    "confidence": 0.85
}

分析要求：
1. 基于《麻衣神相》传统理论
2. 分析要详细且专业
3. 语言要通俗易懂
4. 保持积极正面的态度
5. 提供实用的建议
"""
        
        if analysis_type == "face":
            specific_prompt = """
请重点分析面相特征：
- 五官比例和形状
- 面部轮廓
- 气色和神态
- 根据十二宫理论分析各个部位
- 流年运势分析
"""
        elif analysis_type == "palm":
            specific_prompt = """
请重点分析手相特征：
- 三大主线（生命线、智慧线、感情线）
- 手型和指型
- 掌纹细节
- 手指长度比例
- 指甲形状和颜色
"""
        else:  # auto
            specific_prompt = """
请先判断这是面相还是手相图片，然后进行相应的分析。
如果是面相，重点分析五官和面部特征；
如果是手相，重点分析掌纹和手型特征。
"""
        
        return base_prompt + specific_prompt
    
    def _parse_analysis(self, analysis_text: str, analysis_type: str) -> Dict[str, Any]:
        """解析AI分析结果"""
        try:
            # 尝试解析JSON
            import json
            
            # 清理文本，提取JSON部分
            start_idx = analysis_text.find('{')
            end_idx = analysis_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_text = analysis_text[start_idx:end_idx]
                result = json.loads(json_text)
            else:
                # 如果无法解析JSON，抛出错误
                raise ValueError(f"无法从AI响应中解析JSON格式: {analysis_text[:200]}...")

            # 验证和补充必要字段
            result = self._validate_result(result, analysis_type)

            return result

        except Exception as e:
            logger.error(f"Failed to parse JSON response: {e}")
            raise
    

    
    def _validate_result(self, result: Dict[str, Any], analysis_type: str) -> Dict[str, Any]:
        """验证和补充分析结果"""
        # 确保必要字段存在
        required_fields = ["type", "analysis", "features", "fortune", "suggestions", "confidence"]
        
        for field in required_fields:
            if field not in result:
                if field == "type":
                    result[field] = analysis_type if analysis_type != "auto" else "face"
                elif field == "confidence":
                    result[field] = 0.8
                elif field in ["features", "suggestions"]:
                    result[field] = ["待分析"]
                else:
                    result[field] = "分析中..."
        
        # 确保列表字段不为空
        if not result["features"]:
            result["features"] = ["特征分析中"]
        if not result["suggestions"]:
            result["suggestions"] = ["建议分析中"]
        
        # 确保confidence在合理范围内
        if not isinstance(result["confidence"], (int, float)) or result["confidence"] < 0 or result["confidence"] > 1:
            result["confidence"] = 0.8
        
        return result


# 创建全局OpenAI服务实例
openai_service = OpenAIService()
