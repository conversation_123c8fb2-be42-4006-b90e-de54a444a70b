#!/usr/bin/env python3
"""
测试API配置
"""

import os
import requests
from dotenv import load_dotenv
from openai import OpenAI

# 加载环境变量
load_dotenv()

def test_api_config():
    """测试API配置"""
    print("=== API配置检查 ===\n")
    
    # 检查环境变量
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    model = os.getenv("OPENAI_MODEL")
    max_tokens = os.getenv("OPENAI_MAX_TOKENS")
    temperature = os.getenv("OPENAI_TEMPERATURE")
    
    print("📋 当前配置:")
    print(f"API Key: {api_key[:15] if api_key else 'None'}...")
    print(f"Base URL: {base_url}")
    print(f"Model: {model}")
    print(f"Max Tokens: {max_tokens}")
    print(f"Temperature: {temperature}")
    print()
    
    # 检查配置完整性
    if not api_key:
        print("❌ OPENAI_API_KEY 未配置")
        return False
    
    if not base_url:
        print("❌ OPENAI_BASE_URL 未配置")
        return False
    
    if not model:
        print("❌ OPENAI_MODEL 未配置")
        return False
    
    print("✅ 基本配置完整")
    print()
    
    # 测试网络连接
    print("🌐 测试网络连接...")
    try:
        # 测试基础URL连接
        test_url = base_url.replace('/v1', '') if '/v1' in base_url else base_url
        response = requests.get(test_url, timeout=10)
        print(f"✅ 网络连接正常 (状态码: {response.status_code})")
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接失败 - 无法连接到API服务器")
        return False
    except requests.exceptions.Timeout:
        print("❌ 网络连接超时")
        return False
    except Exception as e:
        print(f"⚠️ 网络测试异常: {e}")
    
    print()
    
    # 测试OpenAI客户端初始化
    print("🔧 测试OpenAI客户端...")
    try:
        client = OpenAI(api_key=api_key, base_url=base_url)
        print("✅ OpenAI客户端初始化成功")
    except Exception as e:
        print(f"❌ OpenAI客户端初始化失败: {e}")
        return False
    
    print()
    
    # 测试简单API调用
    print("🚀 测试API调用...")
    try:
        response = client.chat.completions.create(
            model=model,
            messages=[
                {
                    "role": "user",
                    "content": "Hello, please reply in Chinese."
                }
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        result = response.choices[0].message.content
        print("✅ API调用成功!")
        print(f"📝 AI回复: {result}")
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        
        # 分析错误类型
        error_str = str(e).lower()
        if "connection" in error_str:
            print("💡 建议: 检查网络连接和API服务器状态")
        elif "unauthorized" in error_str or "401" in error_str:
            print("💡 建议: 检查API密钥是否正确")
        elif "model" in error_str or "404" in error_str:
            print("💡 建议: 检查模型名称是否正确")
            print(f"   当前模型: {model}")
            print("   建议尝试: gpt-3.5-turbo, gpt-4, 或咨询API服务商支持的模型")
        elif "timeout" in error_str:
            print("💡 建议: 网络超时，请稍后重试")
        
        return False

def suggest_fixes():
    """建议修复方案"""
    print("\n" + "="*50)
    print("🔧 配置建议:")
    print()
    
    model = os.getenv("OPENAI_MODEL")
    if model == "gemini-2.5-flash":
        print("⚠️ 模型配置建议:")
        print("   当前使用: gemini-2.5-flash (Google Gemini模型)")
        print("   如果API服务不支持Gemini，请尝试:")
        print("   - gpt-3.5-turbo (推荐，速度快)")
        print("   - gpt-4 (质量高)")
        print("   - 或咨询你的API服务商支持哪些模型")
        print()
    
    print("📞 如果问题持续存在:")
    print("1. 联系API服务商确认服务状态")
    print("2. 验证API密钥是否有效")
    print("3. 确认账户余额是否充足")
    print("4. 检查API服务商的模型支持列表")

if __name__ == "__main__":
    success = test_api_config()
    
    if not success:
        suggest_fixes()
    else:
        print("\n🎉 配置测试通过！API工作正常。")
