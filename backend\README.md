# 麻衣神相 API 后端

基于《麻衣神相》传统相术理论的AI分析后端服务。

## 🎉 功能特性

- ✅ **面相分析**: 基于传统相术理论的面部特征分析
- ✅ **手相分析**: 掌纹、手型等手相特征分析  
- ✅ **AI聊天**: 支持与AI进行相术相关的对话
- ✅ **图像上传**: 支持多种图像格式上传
- ✅ **自动代理检测**: 自动检测并配置系统代理
- ✅ **第三方API支持**: 支持OpenAI兼容的第三方API

## 🚀 快速开始

### 1. 环境配置

创建 `.env` 文件：

```env
# OpenAI API配置
OPENAI_API_KEY=your-api-key
OPENAI_BASE_URL=https://your-api-endpoint/v1  # 可选，留空使用官方API
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=3000
OPENAI_TEMPERATURE=0.7

# Supabase配置（可选）
SUPABASE_URL=your-supabase-url
SUPABASE_SERVICE_KEY=your-service-key
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 启动服务

```bash
python run.py
```

服务将在 http://localhost:8000 启动

## 📡 API 端点

### 图像分析
- `POST /api/analyze` - 上传图像进行相术分析

### 聊天对话  
- `POST /api/chat` - 与AI进行相术相关对话

### 文档
- `GET /docs` - API文档
- `GET /chat` - 聊天测试界面

## 🔧 技术架构

- **框架**: FastAPI
- **AI模型**: OpenAI兼容API
- **数据库**: Supabase (可选)
- **图像处理**: PIL + Base64编码
- **代理支持**: 自动检测系统代理配置

## 🌟 核心特性

### 自动代理检测
系统会自动检测并配置：
1. 环境变量代理 (HTTP_PROXY, HTTPS_PROXY)
2. Windows系统代理设置
3. 直接连接

### 智能错误处理
- 网络连接失败时提供详细错误信息
- API调用失败时抛出明确异常
- 配置问题时给出具体建议

### 专业相术分析
基于《麻衣神相》理论，提供：
- 详细的面相/手相特征分析
- 运势预测和建议
- 传统相术理论解释

## 📝 使用示例

### 聊天API
```bash
curl -X POST "http://localhost:8000/api/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "请介绍一下面相学的基本知识"}'
```

### 图像分析API
```bash
curl -X POST "http://localhost:8000/api/analyze" \
  -F "file=@image.jpg" \
  -F "analysis_type=face"
```

## 🔍 故障排除

### 网络连接问题
1. 检查代理设置
2. 验证API密钥和端点
3. 确认防火墙配置

### API调用失败
1. 检查API密钥是否有效
2. 确认模型名称是否正确
3. 验证API端点是否可访问

## 📄 许可证

本项目仅供学习和研究使用。
