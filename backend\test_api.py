#!/usr/bin/env python3
"""
测试API功能的脚本
"""

import requests
import json
from io import BytesIO
from PIL import Image

def test_server_status():
    """测试服务器状态"""
    try:
        response = requests.get("http://localhost:8000/")
        print("服务器状态:")
        print(json.dumps(response.json(), indent=2, ensure_ascii=False))
        return response.json()
    except Exception as e:
        print(f"服务器连接失败: {e}")
        return None

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (300, 300), color='white')
    
    # 保存为字节流
    img_bytes = BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes

def test_analyze_api():
    """测试分析API"""
    try:
        # 创建测试图片
        test_image = create_test_image()
        
        # 准备请求
        files = {
            'image': ('test.jpg', test_image, 'image/jpeg')
        }
        data = {
            'analysis_type': 'face'
        }
        
        print("正在测试图片分析API...")
        response = requests.post(
            "http://localhost:8000/api/analyze",
            files=files,
            data=data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("分析成功!")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return result
        else:
            print(f"分析失败: {response.status_code}")
            print(response.text)
            return None
            
    except Exception as e:
        print(f"API测试失败: {e}")
        return None

if __name__ == "__main__":
    print("=== 麻衣神相API测试 ===\n")
    
    # 测试服务器状态
    status = test_server_status()
    
    if status:
        print(f"\nOpenAI可用: {status.get('openai_available', False)}")
        print(f"Supabase可用: {status.get('supabase_available', False)}")
        
        # 测试分析API
        print("\n" + "="*50)
        result = test_analyze_api()
        
        if result:
            print("\n✅ API测试成功!")
            if "模拟" in result.get('analysis', ''):
                print("⚠️  当前使用模拟数据，请检查OpenAI配置")
            else:
                print("🎉 OpenAI分析正常工作!")
        else:
            print("\n❌ API测试失败")
    else:
        print("❌ 服务器连接失败")
