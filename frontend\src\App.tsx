import React, { useState } from 'react';
import {
  Layout,
  Card,
  Typography,
  Row,
  Col,
  Tabs,
  message
} from 'antd';
import {
  EyeOutlined,
  StarOutlined,
  CameraOutlined
} from '@ant-design/icons';
import ImageUpload from './components/ImageUpload';
import AnalysisResultComponent from './components/AnalysisResult';
import { apiService } from './services/api';
import type { AnalysisResult } from './types';
import './App.css';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

function App() {
  const [loading, setLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);

  const handleUpload = async (file: File) => {
    setLoading(true);

    try {
      // 创建预览URL
      const imageUrl = URL.createObjectURL(file);
      setUploadedImage(imageUrl);

      // 调用API分析图片
      try {
        const result = await apiService.analyzeImage(file);
        setAnalysisResult(result);
        message.success('分析完成！');
      } catch (apiError) {
        // 如果API调用失败，使用模拟数据
        console.warn('API调用失败，使用模拟数据:', apiError);
        const mockResult: AnalysisResult = {
          type: 'face',
          analysis: '根据《麻衣神相》理论分析，您的面相显示出以下特征...',
          features: ['眉清目秀', '鼻梁挺直', '嘴角上扬'],
          fortune: '整体运势较好，事业有成，财运亨通',
          suggestions: ['保持积极心态', '注意身体健康', '把握机遇'],
          confidence: 0.85
        };
        setAnalysisResult(mockResult);
        message.success('分析完成！（演示模式）');
      }

    } catch (error) {
      message.error('上传失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const resetAnalysis = () => {
    setAnalysisResult(null);
    setUploadedImage(null);
  };

  return (
    <Layout className="layout">
      <Header style={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        padding: '0 50px'
      }}>
        <div style={{
          color: 'white',
          fontSize: '24px',
          fontWeight: 'bold',
          lineHeight: '64px'
        }}>
          麻衣神相 - 传统相术智能分析
        </div>
      </Header>

      <Content style={{ padding: '50px', minHeight: 'calc(100vh - 134px)' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <Card>
                <Title level={2} style={{ textAlign: 'center', marginBottom: '30px' }}>
                  <StarOutlined style={{ color: '#faad14' }} /> 智能相术分析
                </Title>
                <Paragraph style={{ textAlign: 'center', fontSize: '16px' }}>
                  基于传统《麻衣神相》理论，结合现代AI技术，为您提供专业的面相和手相分析
                </Paragraph>
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card
                title={
                  <span>
                    <CameraOutlined /> 上传图片
                  </span>
                }
                style={{ height: '100%' }}
              >
                <ImageUpload
                  onUpload={handleUpload}
                  uploadedImage={uploadedImage}
                  onReset={resetAnalysis}
                />
              </Card>
            </Col>

            <Col xs={24} lg={12}>
              <Card
                title={
                  <span>
                    <EyeOutlined /> 分析结果
                  </span>
                }
                style={{ height: '100%' }}
              >
                <AnalysisResultComponent
                  loading={loading}
                  result={analysisResult}
                />
              </Card>
            </Col>
          </Row>

          <Row style={{ marginTop: '24px' }}>
            <Col span={24}>
              <Card title="功能介绍">
                <Tabs defaultActiveKey="1">
                  <TabPane tab="面相分析" key="1">
                    <Paragraph>
                      基于《麻衣神相》中的面相理论，分析五官特征、面部轮廓、气色等，
                      解读性格特点、运势走向、事业财运等方面的信息。
                    </Paragraph>
                  </TabPane>
                  <TabPane tab="手相分析" key="2">
                    <Paragraph>
                      通过分析手掌纹路、手型特征、指甲形状等，
                      解读生命线、智慧线、感情线等主要纹路的含义。
                    </Paragraph>
                  </TabPane>
                  <TabPane tab="使用说明" key="3">
                    <Paragraph>
                      1. 上传清晰的面相或手相图片<br/>
                      2. AI将自动识别并分析图片内容<br/>
                      3. 获得基于传统相术理论的详细分析报告<br/>
                      4. 仅供娱乐参考，请理性对待
                    </Paragraph>
                  </TabPane>
                </Tabs>
              </Card>
            </Col>
          </Row>
        </div>
      </Content>

      <Footer style={{ textAlign: 'center', background: '#f0f2f5' }}>
        麻衣神相 ©2024 - 传统文化与现代科技的结合 | 仅供娱乐参考
      </Footer>
    </Layout>
  );
}

export default App;
