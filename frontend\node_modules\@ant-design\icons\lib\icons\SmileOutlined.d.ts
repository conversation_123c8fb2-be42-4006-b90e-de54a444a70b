import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![smile](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI4OCA0MjFhNDggNDggMCAxMDk2IDAgNDggNDggMCAxMC05NiAwem0zNTIgMGE0OCA0OCAwIDEwOTYgMCA0OCA0OCAwIDEwLTk2IDB6TTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yNjMgNzExYy0zNC4yIDM0LjItNzQgNjEtMTE4LjMgNzkuOEM2MTEgODc0LjIgNTYyLjMgODg0IDUxMiA4ODRjLTUwLjMgMC05OS05LjgtMTQ0LjgtMjkuMkEzNzAuNCAzNzAuNCAwIDAxMjQ4LjkgNzc1Yy0zNC4yLTM0LjItNjEtNzQtNzkuOC0xMTguM0MxNDkuOCA2MTEgMTQwIDU2Mi4zIDE0MCA1MTJzOS44LTk5IDI5LjItMTQ0LjhBMzcwLjQgMzcwLjQgMCAwMTI0OSAyNDguOWMzNC4yLTM0LjIgNzQtNjEgMTE4LjMtNzkuOEM0MTMgMTQ5LjggNDYxLjcgMTQwIDUxMiAxNDBjNTAuMyAwIDk5IDkuOCAxNDQuOCAyOS4yQTM3MC40IDM3MC40IDAgMDE3NzUuMSAyNDljMzQuMiAzNC4yIDYxIDc0IDc5LjggMTE4LjNDODc0LjIgNDEzIDg4NCA0NjEuNyA4ODQgNTEycy05LjggOTktMjkuMiAxNDQuOEEzNjguODkgMzY4Ljg5IDAgMDE3NzUgNzc1ek02NjQgNTMzaC00OC4xYy00LjIgMC03LjggMy4yLTguMSA3LjRDNjA0IDU4OS45IDU2Mi41IDYyOSA1MTIgNjI5cy05Mi4xLTM5LjEtOTUuOC04OC42Yy0uMy00LjItMy45LTcuNC04LjEtNy40SDM2MGE4IDggMCAwMC04IDguNGM0LjQgODQuMyA3NC41IDE1MS42IDE2MCAxNTEuNnMxNTUuNi02Ny4zIDE2MC0xNTEuNmE4IDggMCAwMC04LTguNHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
