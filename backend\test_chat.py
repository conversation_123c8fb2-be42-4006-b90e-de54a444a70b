#!/usr/bin/env python3
"""
测试聊天API
"""

import requests
import json

def test_chat_api():
    """测试聊天API"""
    try:
        # 测试消息
        test_message = "你好，请用中文回复"
        
        print(f"发送消息: {test_message}")
        
        response = requests.post(
            "http://localhost:8000/api/chat",
            json={"message": test_message},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 聊天API测试成功!")
            print(f"AI回复: {result['response']}")
            print(f"模型: {result['model']}")
            print(f"状态: {result['status']}")
            return True
        else:
            print(f"❌ 聊天API测试失败: {response.status_code}")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"❌ 聊天API测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 聊天API测试 ===\n")
    test_chat_api()
