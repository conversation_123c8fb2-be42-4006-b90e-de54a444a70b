#!/usr/bin/env python3
"""
检查配置文件的工具
"""

import os
from dotenv import load_dotenv

def check_config():
    """检查配置文件"""
    print("=== 配置检查工具 ===\n")
    
    # 加载环境变量
    load_dotenv()
    
    print("📁 环境变量文件: .env")
    print("🔍 检查配置项...\n")
    
    # 检查OpenAI配置
    print("🤖 OpenAI API 配置:")
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL")
    model = os.getenv("OPENAI_MODEL")
    max_tokens = os.getenv("OPENAI_MAX_TOKENS")
    temperature = os.getenv("OPENAI_TEMPERATURE")
    
    print(f"  API密钥: {api_key[:15] if api_key else 'None'}...")
    print(f"  基础URL: {base_url}")
    print(f"  模型: {model}")
    print(f"  最大令牌: {max_tokens}")
    print(f"  温度: {temperature}")
    
    # 检查配置问题
    issues = []
    
    if not api_key or api_key.startswith("请填入"):
        issues.append("❌ API密钥未配置或为默认值")
    else:
        print("  ✅ API密钥已配置")
    
    if not base_url or base_url.startswith("请填入"):
        issues.append("❌ 基础URL未配置或为默认值")
    else:
        print("  ✅ 基础URL已配置")
        
        # 检查URL格式
        if "/chat/completions" in base_url:
            issues.append("⚠️ 基础URL包含完整端点路径，应该只包含基础域名")
            print(f"  建议修改为: {base_url.replace('/chat/completions', '')}")
    
    if not model:
        issues.append("❌ 模型未配置")
    else:
        print(f"  ✅ 模型已配置: {model}")
        
        # 检查模型兼容性
        if model == "o3":
            print("  ℹ️ 注意: o3模型可能不支持vision功能")
        elif "gemini" in model.lower():
            print("  ℹ️ 注意: Gemini模型，确保API兼容OpenAI格式")
    
    print(f"\n🗄️ Supabase 配置:")
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_KEY")
    
    print(f"  URL: {supabase_url}")
    print(f"  密钥: {supabase_key[:15] if supabase_key else 'None'}...")
    
    if not supabase_key or supabase_key.startswith("请填入"):
        print("  ⚠️ Supabase未配置（可选）")
    else:
        print("  ✅ Supabase已配置")
    
    print(f"\n📊 总结:")
    if issues:
        print("发现以下问题:")
        for issue in issues:
            print(f"  {issue}")
    else:
        print("✅ 配置检查通过！")
    
    print(f"\n🔧 建议的配置格式:")
    print("OPENAI_API_KEY=sk-your-api-key-here")
    print("OPENAI_BASE_URL=https://your-api-domain.com/v1")
    print("OPENAI_MODEL=gpt-3.5-turbo 或 gpt-4 或其他兼容模型")
    print("OPENAI_MAX_TOKENS=1500")
    print("OPENAI_TEMPERATURE=0.7")

if __name__ == "__main__":
    check_config()
