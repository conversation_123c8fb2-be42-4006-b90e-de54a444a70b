import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![gateway](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkyOCAzOTJjOC44IDAgMTYtNy4yIDE2LTE2VjE5MmMwLTguOC03LjItMTYtMTYtMTZINzQ0Yy04LjggMC0xNiA3LjItMTYgMTZ2NTZIMjk2di01NmMwLTguOC03LjItMTYtMTYtMTZIOTZjLTguOCAwLTE2IDcuMi0xNiAxNnYxODRjMCA4LjggNy4yIDE2IDE2IDE2aDU2djI0MEg5NmMtOC44IDAtMTYgNy4yLTE2IDE2djE4NGMwIDguOCA3LjIgMTYgMTYgMTZoMTg0YzguOCAwIDE2LTcuMiAxNi0xNnYtNTZoNDMydjU2YzAgOC44IDcuMiAxNiAxNiAxNmgxODRjOC44IDAgMTYtNy4yIDE2LTE2VjY0OGMwLTguOC03LjItMTYtMTYtMTZoLTU2VjM5Mmg1NnpNNzkyIDI0MGg4OHY4OGgtODh2LTg4em0tNjQ4IDg4di04OGg4OHY4OGgtODh6bTg4IDQ1NmgtODh2LTg4aDg4djg4em02NDgtODh2ODhoLTg4di04OGg4OHptLTgwLTY0aC01NmMtOC44IDAtMTYgNy4yLTE2IDE2djU2SDI5NnYtNTZjMC04LjgtNy4yLTE2LTE2LTE2aC01NlYzOTJoNTZjOC44IDAgMTYtNy4yIDE2LTE2di01Nmg0MzJ2NTZjMCA4LjggNy4yIDE2IDE2IDE2aDU2djI0MHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
