import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![layout](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM4NCA5MTJoNDk2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjM0MEgzODR2NTcyem00OTYtODAwSDM4NHYxNjRoNTI4VjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTc2OCAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoMTc2VjExMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMyeiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
