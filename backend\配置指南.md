# 麻衣神相API配置指南

## 🔧 第三方OpenAI API配置

### 1. 编辑环境变量文件

打开 `backend/.env` 文件，填入你的第三方API信息：

```env
# OpenAI配置 - 第三方API
OPENAI_API_KEY=你的第三方API密钥
OPENAI_BASE_URL=你的第三方API端点地址
OPENAI_MODEL=gpt-4-vision-preview
OPENAI_MAX_TOKENS=1500
OPENAI_TEMPERATURE=0.7
```

### 2. 常见第三方API配置示例

#### 示例1：通用第三方API
```env
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_BASE_URL=https://api.your-provider.com/v1
```

#### 示例2：国内API服务商
```env
OPENAI_API_KEY=your-api-key-here
OPENAI_BASE_URL=https://api.provider.cn/v1
```

### 3. 支持的模型

根据你的第三方API提供商，可以使用以下模型：
- `gpt-4-vision-preview` (推荐，支持图像分析)
- `gpt-4`
- `gpt-3.5-turbo`

### 4. 参数说明

- **OPENAI_API_KEY**: 你的API密钥
- **OPENAI_BASE_URL**: 第三方API的基础URL（必须以/v1结尾）
- **OPENAI_MODEL**: 使用的模型名称
- **OPENAI_MAX_TOKENS**: 最大返回token数量
- **OPENAI_TEMPERATURE**: 创造性参数（0-1之间）

## 🗄️ Supabase数据库配置

### 1. 获取Supabase配置信息

1. 登录 [Supabase](https://supabase.com)
2. 进入你的项目：`papzztaomhzhwtcnvhhf`
3. 在项目设置中找到API配置

### 2. 填入配置信息

```env
# Supabase配置
SUPABASE_URL=https://papzztaomhzhwtcnvhhf.supabase.co
SUPABASE_KEY=你的supabase_anon_key
SUPABASE_SERVICE_KEY=你的supabase_service_key
```

### 3. 创建数据库表

在Supabase SQL编辑器中运行 `database_schema.sql` 文件中的SQL语句。

## 🚀 启动服务

配置完成后，重启服务器：

```bash
cd backend
python complete_server.py
```

## 🧪 测试配置

### 1. 检查服务状态

访问：http://localhost:8000/

返回示例：
```json
{
  "message": "欢迎使用麻衣神相API",
  "version": "1.0.0",
  "status": "running",
  "openai_available": true,
  "supabase_available": true
}
```

### 2. 测试图片分析

使用前端界面上传图片，或者使用API工具测试：

```bash
curl -X POST "http://localhost:8000/api/analyze" \
  -F "image=@test_image.jpg" \
  -F "analysis_type=face"
```

## ❗ 常见问题

### 1. OpenAI API连接失败

- 检查API密钥是否正确
- 确认BASE_URL格式正确（必须以/v1结尾）
- 检查网络连接

### 2. Supabase连接失败

- 确认URL和密钥正确
- 检查数据库表是否已创建
- 确认RLS策略配置正确

### 3. 图片分析失败

- 确认图片格式支持（jpg, png, webp）
- 检查图片大小（不超过10MB）
- 查看服务器日志获取详细错误信息

## 📝 日志查看

服务器日志会显示详细的运行信息：
- OpenAI API调用状态
- Supabase数据库操作
- 错误信息和调试信息

## 🔒 安全建议

1. 不要将API密钥提交到版本控制
2. 使用环境变量管理敏感信息
3. 定期更换API密钥
4. 配置适当的CORS策略
