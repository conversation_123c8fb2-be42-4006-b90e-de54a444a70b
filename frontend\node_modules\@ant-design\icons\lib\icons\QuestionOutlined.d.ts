import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![question](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTc2NCAyODAuOWMtMTQtMzAuNi0zMy45LTU4LjEtNTkuMy04MS42QzY1My4xIDE1MS40IDU4NC42IDEyNSA1MTIgMTI1cy0xNDEuMSAyNi40LTE5Mi43IDc0LjJjLTI1LjQgMjMuNi00NS4zIDUxLTU5LjMgODEuNy0xNC42IDMyLTIyIDY1LjktMjIgMTAwLjl2MjdjMCA2LjIgNSAxMS4yIDExLjIgMTEuMmg1NGM2LjIgMCAxMS4yLTUgMTEuMi0xMS4ydi0yN2MwLTk5LjUgODguNi0xODAuNCAxOTcuNi0xODAuNHMxOTcuNiA4MC45IDE5Ny42IDE4MC40YzAgNDAuOC0xNC41IDc5LjItNDIgMTExLjItMjcuMiAzMS43LTY1LjYgNTQuNC0xMDguMSA2NC0yNC4zIDUuNS00Ni4yIDE5LjItNjEuNyAzOC44YTExMC44NSAxMTAuODUgMCAwMC0yMy45IDY4LjZ2MzEuNGMwIDYuMiA1IDExLjIgMTEuMiAxMS4yaDU0YzYuMiAwIDExLjItNSAxMS4yLTExLjJ2LTMxLjRjMC0xNS43IDEwLjktMjkuNSAyNi0zMi45IDU4LjQtMTMuMiAxMTEuNC00NC43IDE0OS4zLTg4LjcgMTkuMS0yMi4zIDM0LTQ3LjEgNDQuMy03NCAxMC43LTI3LjkgMTYuMS01Ny4yIDE2LjEtODcgMC0zNS03LjQtNjktMjItMTAwLjl6TTUxMiA3ODdjLTMwLjkgMC01NiAyNS4xLTU2IDU2czI1LjEgNTYgNTYgNTYgNTYtMjUuMSA1Ni01Ni0yNS4xLTU2LTU2LTU2eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
