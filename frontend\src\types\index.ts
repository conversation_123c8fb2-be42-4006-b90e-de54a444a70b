// 共享类型定义

// 分析结果接口
export interface AnalysisResult {
  type: 'face' | 'palm';
  analysis: string;
  features: string[];
  fortune: string;
  suggestions: string[];
  confidence: number;
}

// 用户接口
export interface User {
  id: string;
  email: string;
  name: string;
  created_at: string;
}

// API响应接口
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 上传文件接口
export interface UploadResponse {
  url: string;
  filename: string;
  size: number;
}
