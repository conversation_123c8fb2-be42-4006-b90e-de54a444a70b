#!/usr/bin/env python3
"""
代理配置辅助工具
"""

import os
import winreg
import requests
import logging
from typing import Optional, Dict

logger = logging.getLogger(__name__)

def get_system_proxy() -> Optional[Dict[str, str]]:
    """获取Windows系统代理设置"""
    try:
        # 读取Windows注册表中的代理设置
        with winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                           r"Software\Microsoft\Windows\CurrentVersion\Internet Settings") as key:
            
            # 检查是否启用代理
            proxy_enable, _ = winreg.QueryValueEx(key, "ProxyEnable")
            if not proxy_enable:
                return None
            
            # 获取代理服务器地址
            proxy_server, _ = winreg.QueryValueEx(key, "ProxyServer")
            
            # 解析代理设置
            if "=" in proxy_server:
                # 分协议的代理设置
                proxies = {}
                for item in proxy_server.split(";"):
                    if "=" in item:
                        protocol, address = item.split("=", 1)
                        # 只保留http和https协议
                        if protocol.lower() in ['http', 'https']:
                            proxies[protocol.lower()] = f"http://{address}"
                return proxies if proxies else None
            else:
                # 通用代理设置
                return {
                    "http": f"http://{proxy_server}",
                    "https": f"http://{proxy_server}"
                }
    except Exception as e:
        logger.debug(f"无法读取系统代理设置: {e}")
        return None

def get_env_proxy() -> Optional[Dict[str, str]]:
    """获取环境变量代理设置"""
    env_proxies = {}
    for var in ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']:
        value = os.environ.get(var)
        if value:
            protocol = var.lower().replace('_proxy', '')
            env_proxies[protocol] = value
    
    return env_proxies if env_proxies else None

def test_proxy_connectivity(proxies: Optional[Dict[str, str]] = None) -> bool:
    """测试代理连接"""
    test_urls = [
        "https://www.baidu.com",
        "https://httpbin.org/ip"
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url, proxies=proxies, timeout=10)
            if response.status_code == 200:
                return True
        except Exception:
            continue
    
    return False

def get_working_proxy() -> Optional[Dict[str, str]]:
    """获取可用的代理配置"""
    
    # 1. 检查环境变量代理
    env_proxies = get_env_proxy()
    if env_proxies:
        logger.info(f"检测到环境变量代理: {env_proxies}")
        if test_proxy_connectivity(env_proxies):
            logger.info("环境变量代理可用")
            return env_proxies
    
    # 2. 检查系统代理设置
    system_proxies = get_system_proxy()
    if system_proxies:
        logger.info(f"检测到系统代理: {system_proxies}")
        if test_proxy_connectivity(system_proxies):
            logger.info("系统代理可用")
            return system_proxies
    
    # 3. 测试无代理连接
    if test_proxy_connectivity(None):
        logger.info("直接连接可用")
        return None
    
    logger.warning("未找到可用的网络配置")
    return None

def configure_openai_proxy():
    """为OpenAI配置代理"""
    working_proxy = get_working_proxy()
    
    if working_proxy:
        # 设置环境变量，让OpenAI客户端使用代理
        if 'http' in working_proxy:
            os.environ['HTTP_PROXY'] = working_proxy['http']
        if 'https' in working_proxy:
            os.environ['HTTPS_PROXY'] = working_proxy['https']
        
        logger.info(f"已为OpenAI配置代理: {working_proxy}")
        return working_proxy
    else:
        # 清除代理环境变量
        for var in ['HTTP_PROXY', 'HTTPS_PROXY']:
            if var in os.environ:
                del os.environ[var]
        
        logger.info("使用直接连接")
        return None
