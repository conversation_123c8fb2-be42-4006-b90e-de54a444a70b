import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![highlight](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTk1Ny42IDUwNy40TDYwMy4yIDE1OC4yYTcuOSA3LjkgMCAwMC0xMS4yIDBMMzUzLjMgMzkzLjRhOC4wMyA4LjAzIDAgMDAtLjEgMTEuM2wuMS4xIDQwIDM5LjQtMTE3LjIgMTE1LjNhOC4wMyA4LjAzIDAgMDAtLjEgMTEuM2wuMS4xIDM5LjUgMzguOS0xODkuMSAxODdINzIuMWMtNC40IDAtOC4xIDMuNi04LjEgOFY4NjBjMCA0LjQgMy42IDggOCA4aDM0NC45YzIuMSAwIDQuMS0uOCA1LjYtMi4zbDc2LjEtNzUuNiA0MC40IDM5LjhhNy45IDcuOSAwIDAwMTEuMiAwbDExNy4xLTExNS42IDQwLjEgMzkuNWE3LjkgNy45IDAgMDAxMS4yIDBsMjM4LjctMjM1LjJjMy40LTMgMy40LTggLjMtMTEuMnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
