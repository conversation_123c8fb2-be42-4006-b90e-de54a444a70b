from supabase import create_client, Client
from core.config import settings
import logging

logger = logging.getLogger(__name__)


class SupabaseClient:
    """Supabase数据库客户端"""

    def __init__(self):
        self.client: Client = None
        self.is_configured = self._check_configuration()
        if self.is_configured:
            self.connect()

    def _check_configuration(self) -> bool:
        """检查Supabase配置是否完整"""
        try:
            url = getattr(settings, 'supabase_url', None)
            key = getattr(settings, 'supabase_service_key', None)

            if not url or not key or key.startswith("请填入"):
                logger.warning("Supabase configuration not found or incomplete")
                return False
            return True
        except Exception:
            return False

    def connect(self):
        """连接到Supabase"""
        if not self.is_configured:
            logger.warning("Supabase not configured, skipping connection")
            return

        try:
            self.client = create_client(
                settings.supabase_url,
                settings.supabase_service_key
            )
            logger.info("Successfully connected to Supabase")
        except Exception as e:
            logger.error(f"Failed to connect to Supabase: {e}")
            self.client = None

    def get_client(self) -> Client:
        """获取Supabase客户端"""
        if not self.is_configured:
            raise Exception("Supabase not configured")

        if not self.client:
            self.connect()

        if not self.client:
            raise Exception("Failed to connect to Supabase")

        return self.client


# 创建全局数据库实例
db = SupabaseClient()