-- 麻衣神相数据库表结构

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('user', 'premium', 'admin')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 分析结果表
CREATE TABLE IF NOT EXISTS analysis_results (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('face', 'palm')),
    analysis TEXT NOT NULL,
    features JSONB NOT NULL DEFAULT '[]',
    fortune TEXT NOT NULL,
    suggestions JSONB NOT NULL DEFAULT '[]',
    confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
    image_url VARCHAR(500),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 用户分析历史表（可选，用于更复杂的历史记录）
CREATE TABLE IF NOT EXISTS user_analysis_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    analysis_id UUID NOT NULL REFERENCES analysis_results(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, analysis_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_analysis_results_user_id ON analysis_results(user_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_created_at ON analysis_results(created_at);
CREATE INDEX IF NOT EXISTS idx_analysis_results_type ON analysis_results(type);
CREATE INDEX IF NOT EXISTS idx_user_analysis_history_user_id ON user_analysis_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_analysis_history_created_at ON user_analysis_history(created_at);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为用户表添加更新时间触发器
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全策略（RLS）
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_analysis_history ENABLE ROW LEVEL SECURITY;

-- 用户表的RLS策略
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- 分析结果表的RLS策略
CREATE POLICY "Users can view own analysis results" ON analysis_results
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert analysis results" ON analysis_results
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update own analysis results" ON analysis_results
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own analysis results" ON analysis_results
    FOR DELETE USING (auth.uid() = user_id);

-- 用户分析历史表的RLS策略
CREATE POLICY "Users can view own analysis history" ON user_analysis_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own analysis history" ON user_analysis_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own analysis history" ON user_analysis_history
    FOR DELETE USING (auth.uid() = user_id);

-- 插入一些示例数据（可选）
-- INSERT INTO users (email, password_hash, name, role) VALUES 
-- ('<EMAIL>', '$2b$12$example_hash', '管理员', 'admin'),
-- ('<EMAIL>', '$2b$12$example_hash', '测试用户', 'user');

-- 创建视图：用户分析统计
CREATE OR REPLACE VIEW user_analysis_stats AS
SELECT 
    u.id as user_id,
    u.name,
    u.email,
    COUNT(ar.id) as total_analyses,
    COUNT(CASE WHEN ar.type = 'face' THEN 1 END) as face_analyses,
    COUNT(CASE WHEN ar.type = 'palm' THEN 1 END) as palm_analyses,
    AVG(ar.confidence) as avg_confidence,
    MAX(ar.created_at) as last_analysis_date
FROM users u
LEFT JOIN analysis_results ar ON u.id = ar.user_id
GROUP BY u.id, u.name, u.email;

-- 创建函数：获取用户最近的分析结果
CREATE OR REPLACE FUNCTION get_user_recent_analyses(user_uuid UUID, limit_count INTEGER DEFAULT 10)
RETURNS TABLE (
    id UUID,
    type VARCHAR,
    analysis TEXT,
    confidence DECIMAL,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT ar.id, ar.type, ar.analysis, ar.confidence, ar.created_at
    FROM analysis_results ar
    WHERE ar.user_id = user_uuid
    ORDER BY ar.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
