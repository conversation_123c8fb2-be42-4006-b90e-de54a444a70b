import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![property-safety](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2Ni45IDE2OS45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNNjQ4LjMgMzMyLjhsLTg3LjcgMTYxLjFoNDUuN2M1LjUgMCAxMCA0LjUgMTAgMTB2MjEuM2MwIDUuNS00LjUgMTAtMTAgMTBoLTYzLjR2MjkuN2g2My40YzUuNSAwIDEwIDQuNSAxMCAxMHYyMS4zYzAgNS41LTQuNSAxMC0xMCAxMGgtNjMuNFY2NThjMCA1LjUtNC41IDEwLTEwIDEwaC00MS4zYy01LjUgMC0xMC00LjUtMTAtMTB2LTUxLjhoLTYzLjFjLTUuNSAwLTEwLTQuNS0xMC0xMHYtMjEuM2MwLTUuNSA0LjUtMTAgMTAtMTBoNjMuMXYtMjkuN2gtNjMuMWMtNS41IDAtMTAtNC41LTEwLTEwdi0yMS4zYzAtNS41IDQuNS0xMCAxMC0xMGg0NS4ybC04OC0xNjEuMWMtMi42LTQuOC0uOS0xMC45IDQtMTMuNiAxLjUtLjggMy4xLTEuMiA0LjgtMS4yaDQ2YzMuOCAwIDcuMiAyLjEgOC45IDUuNWw3Mi45IDE0NC4zIDczLjItMTQ0LjNhMTAgMTAgMCAwMTguOS01LjVoNDVjNS41IDAgMTAgNC41IDEwIDEwIC4xIDEuNy0uMyAzLjMtMS4xIDQuOHoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
