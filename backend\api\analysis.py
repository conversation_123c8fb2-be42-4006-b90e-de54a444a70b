import os
import uuid
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from models.analysis import AnalysisResult, AnalysisRequest, AnalysisType
from services.openai_service import openai_service
from core.database import db
from core.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["analysis"])


class ChatRequest(BaseModel):
    message: str


class ChatResponse(BaseModel):
    id: str
    message: str
    response: str
    model: str
    created_at: str
    status: str


@router.post("/analyze", response_model=AnalysisResult)
async def analyze_image(
    image: UploadFile = File(...),
    analysis_type: str = Form(default="auto")
):
    """
    分析上传的图片（面相或手相）
    """
    try:
        # 验证文件类型
        if not image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="文件必须是图片格式")

        # 验证文件大小
        contents = await image.read()
        if len(contents) > settings.max_file_size:
            raise HTTPException(status_code=400, detail="文件大小超过限制")

        # 验证分析类型
        try:
            analysis_type_enum = AnalysisType(analysis_type)
        except ValueError:
            analysis_type_enum = AnalysisType.AUTO

        # 调用OpenAI分析
        analysis_result = await openai_service.analyze_physiognomy(
            contents,
            analysis_type_enum.value
        )

        # 生成唯一ID
        analysis_id = str(uuid.uuid4())

        # 保存图片文件（可选）
        image_url = None
        if settings.upload_dir:
            os.makedirs(settings.upload_dir, exist_ok=True)
            file_extension = os.path.splitext(image.filename)[1]
            filename = f"{analysis_id}{file_extension}"
            file_path = os.path.join(settings.upload_dir, filename)

            with open(file_path, "wb") as f:
                f.write(contents)

            image_url = f"/uploads/{filename}"

        # 创建分析结果对象
        result = AnalysisResult(
            id=analysis_id,
            type=analysis_result["type"],
            analysis=analysis_result["analysis"],
            features=analysis_result["features"],
            fortune=analysis_result["fortune"],
            suggestions=analysis_result["suggestions"],
            confidence=analysis_result["confidence"],
            created_at=datetime.utcnow(),
            image_url=image_url
        )

        # 保存到数据库（可选，如果用户已登录）
        try:
            supabase_client = db.get_client()
            supabase_client.table("analysis_results").insert({
                "id": analysis_id,
                "type": result.type,
                "analysis": result.analysis,
                "features": result.features,
                "fortune": result.fortune,
                "suggestions": result.suggestions,
                "confidence": result.confidence,
                "image_url": result.image_url,
                "created_at": result.created_at.isoformat()
            }).execute()
            logger.info(f"Saved analysis result to database: {analysis_id}")
        except Exception as e:
            logger.warning(f"Failed to save to database: {e}")
            # 不影响主要功能，继续返回结果

        logger.info(f"Successfully analyzed image: {analysis_id}")
        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing image: {e}")
        raise HTTPException(status_code=500, detail="分析过程中发生错误，请重试")


@router.get("/analysis/{analysis_id}", response_model=AnalysisResult)
async def get_analysis(analysis_id: str):
    """
    获取指定的分析结果
    """
    try:
        supabase_client = db.get_client()
        response = supabase_client.table("analysis_results").select("*").eq("id", analysis_id).execute()

        if not response.data:
            raise HTTPException(status_code=404, detail="分析结果不存在")

        data = response.data[0]
        return AnalysisResult(**data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis: {e}")
        raise HTTPException(status_code=500, detail="获取分析结果失败")


@router.get("/analysis", response_model=List[AnalysisResult])
async def get_analysis_history(
    limit: int = 10,
    offset: int = 0
):
    """
    获取分析历史记录
    """
    try:
        supabase_client = db.get_client()
        response = supabase_client.table("analysis_results")\
            .select("*")\
            .order("created_at", desc=True)\
            .range(offset, offset + limit - 1)\
            .execute()

        return [AnalysisResult(**item) for item in response.data]

    except Exception as e:
        logger.error(f"Error getting analysis history: {e}")
        raise HTTPException(status_code=500, detail="获取历史记录失败")


@router.delete("/analysis/{analysis_id}")
async def delete_analysis(analysis_id: str):
    """
    删除指定的分析结果
    """
    try:
        supabase_client = db.get_client()
        response = supabase_client.table("analysis_results").delete().eq("id", analysis_id).execute()

        if not response.data:
            raise HTTPException(status_code=404, detail="分析结果不存在")

        return {"message": "分析结果已删除"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting analysis: {e}")
        raise HTTPException(status_code=500, detail="删除分析结果失败")


@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest):
    """
    AI聊天接口，用于测试OpenAI连接
    """
    try:
        message = request.message
        if not message:
            raise HTTPException(status_code=400, detail="消息内容不能为空")

        chat_id = str(uuid.uuid4())
        logger.info(f"Starting chat request {chat_id}: {message[:50]}...")

        try:
            # 调用OpenAI服务进行聊天
            response = await openai_service.chat(message)

            logger.info(f"✅ OpenAI chat completed successfully for {chat_id}")

            return ChatResponse(
                id=chat_id,
                message=message,
                response=response,
                model=settings.openai_model,
                created_at=datetime.now().isoformat(),
                status="success"
            )

        except Exception as e:
            logger.warning(f"⚠️ OpenAI chat failed: {e}")

            # 抛出HTTP异常
            raise HTTPException(status_code=500, detail=f"AI服务错误: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail="聊天服务发生错误")