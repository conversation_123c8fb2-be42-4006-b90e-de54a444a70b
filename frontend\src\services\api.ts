import axios from 'axios';
import type { AnalysisResult, User } from '../types';

// 配置API基础URL
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 类型定义已移至 ../types/index.ts

// API方法
export const apiService = {
  // 图片分析
  analyzeImage: async (file: File): Promise<AnalysisResult> => {
    const formData = new FormData();
    formData.append('image', file);
    
    return api.post('/api/analyze', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 用户认证
  login: async (email: string, password: string): Promise<{ token: string; user: User }> => {
    return api.post('/api/auth/login', { email, password });
  },

  register: async (email: string, password: string, name: string): Promise<{ token: string; user: User }> => {
    return api.post('/api/auth/register', { email, password, name });
  },

  // 获取分析历史
  getAnalysisHistory: async (): Promise<AnalysisResult[]> => {
    return api.get('/api/history');
  },

  // 保存分析结果
  saveAnalysis: async (result: AnalysisResult): Promise<void> => {
    return api.post('/api/history', result);
  },

  // 获取用户信息
  getUserProfile: async (): Promise<User> => {
    return api.get('/api/user/profile');
  },
};

export default api;
