import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![spotify](data:image/svg+xml;base64,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) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
