import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![insert-row-left](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHN0eWxlIC8+PC9kZWZzPjxwYXRoIGQ9Ik0yNDggMTEyaC04MGMtNC40IDAtOCAzLjYtOCA4djc4NGMwIDQuNCAzLjYgOCA4IDhoODBjNC40IDAgOC0zLjYgOC04VjEyMGMwLTQuNC0zLjYtOC04LTh6bTU4NCAwSDM2OGMtMTcuNyAwLTMyIDE0LjktMzIgMzMuM3Y3MzMuM2MwIDE4LjQgMTQuMyAzMy4zIDMyIDMzLjNoNDY0YzE3LjcgMCAzMi0xNC45IDMyLTMzLjNWMTQ1LjNjMC0xOC40LTE0LjMtMzMuMy0zMi0zMy4zek01NjggODQwSDQwOFY2NjRoMTYwdjE3NnptMC0yNDBINDA4VjQyNGgxNjB2MTc2em0wLTI0MEg0MDhWMTg0aDE2MHYxNzZ6bTIyNCA0ODBINjMyVjY2NGgxNjB2MTc2em0wLTI0MEg2MzJWNDI0aDE2MHYxNzZ6bTAtMjQwSDYzMlYxODRoMTYwdjE3NnoiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
