import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![medicine-box](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzOS4yIDI3OC4xYTMyIDMyIDAgMDAtMzAuNC0yMi4xSDczNlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJIMzIwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYxMTJoLTcyLjhhMzEuOSAzMS45IDAgMDAtMzAuNCAyMi4xTDExMiA1MDJ2Mzc4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlY1MDJsLTcyLjgtMjIzLjl6TTY2MCA2MjhjMCA0LjQtMy42IDgtOCA4SDU0NHYxMDhjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04VjYzNkgzNzJjLTQuNCAwLTgtMy42LTgtOHYtNDhjMC00LjQgMy42LTggOC04aDEwOFY0NjRjMC00LjQgMy42LTggOC04aDQ4YzQuNCAwIDggMy42IDggOHYxMDhoMTA4YzQuNCAwIDggMy42IDggOHY0OHptNC0zNzJIMzYwdi03MmgzMDR2NzJ6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
