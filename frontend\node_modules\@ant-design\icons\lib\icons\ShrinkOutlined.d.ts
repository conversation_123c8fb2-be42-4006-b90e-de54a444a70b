import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![shrink](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MS43IDE4Ny40bC00NS4xLTQ1LjFhOC4wMyA4LjAzIDAgMDAtMTEuMyAwTDY2Ny44IDI5OS45bC01NC43LTU0LjdhNy45NCA3Ljk0IDAgMDAtMTMuNSA0LjdMNTc2LjEgNDM5Yy0uNiA1LjIgMy43IDkuNSA4LjkgOC45bDE4OS4yLTIzLjVjNi42LS44IDkuMy04LjggNC43LTEzLjVsLTU0LjctNTQuNyAxNTcuNi0xNTcuNmMzLTMgMy04LjEtLjEtMTEuMnpNNDM5IDU3Ni4xbC0xODkuMiAyMy41Yy02LjYuOC05LjMgOC45LTQuNyAxMy41bDU0LjcgNTQuNy0xNTcuNSAxNTcuNWE4LjAzIDguMDMgMCAwMDAgMTEuM2w0NS4xIDQ1LjFjMy4xIDMuMSA4LjIgMy4xIDExLjMgMGwxNTcuNi0xNTcuNiA1NC43IDU0LjdhNy45NCA3Ljk0IDAgMDAxMy41LTQuN0w0NDcuOSA1ODVhNy45IDcuOSAwIDAwLTguOS04Ljl6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
