#!/usr/bin/env python3
"""
简化的测试服务器，用于验证基本功能
"""

import os
import sys
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 配置基本日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="麻衣神相API - 测试版",
    version="1.0.0",
    description="基于《麻衣神相》的AI相术分析API - 测试版本"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "欢迎使用麻衣神相API - 测试版",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "message": "服务器运行正常"
    }

@app.post("/api/analyze")
async def analyze_image_mock():
    """模拟分析接口"""
    return {
        "id": "test-analysis-123",
        "type": "face",
        "analysis": "这是一个测试分析结果。根据《麻衣神相》理论，此面相显示出智慧与福气并存的特征。",
        "features": ["眉清目秀", "鼻梁挺直", "嘴角上扬"],
        "fortune": "整体运势良好，事业有成，财运亨通。",
        "suggestions": ["保持积极心态", "注意身体健康", "把握发展机遇"],
        "confidence": 0.85,
        "created_at": "2025-08-02T12:00:00Z"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
